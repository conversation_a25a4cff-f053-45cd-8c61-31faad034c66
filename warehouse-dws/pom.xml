<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.fxiaoke</groupId>
    <artifactId>fs-bi-warehouse</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>

  <artifactId>warehouse-dws</artifactId>
  <version>1.0.0-SNAPSHOT</version>
  <packaging>war</packaging>
  <properties>
    <fs-organization-adapter-api.version>3.0.0-SNAPSHOT</fs-organization-adapter-api.version>
    <ehcache.version>3.8.1</ehcache.version>
  </properties>
  <dependencies>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-rocketmq-support</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.rocketmq</groupId>
      <artifactId>rocketmq-client</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.rocketmq</groupId>
      <artifactId>rocketmq-acl</artifactId>
    </dependency>
    <dependency>
      <groupId>com.clickhouse</groupId>
      <artifactId>clickhouse-jdbc</artifactId>
      <version>0.7.1</version>
      <exclusions>
        <exclusion>
          <artifactId>httpclient5</artifactId>
          <groupId>org.apache.httpcomponents.client5</groupId>
        </exclusion>
      </exclusions>
      <!-- use uber jar with all dependencies included, change classifier to http for smaller jar -->
<!--      <classifier>shaded-all</classifier>-->
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents.client5</groupId>
      <artifactId>httpclient5</artifactId>
      <version>5.3.1</version>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>warehouse-common</artifactId>
      <version>1.6.2-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>fs-pod-client</artifactId>
          <groupId>com.facishare</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>mybatis-spring-support</artifactId>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-enterprise-id-account-converter</artifactId>
      <version>1.1-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke.common</groupId>
      <artifactId>jdbc-support</artifactId>
    </dependency>
    <dependency>
      <groupId>commons-collections</groupId>
      <artifactId>commons-collections</artifactId>
      <version>3.2.2</version>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-organization-adapter-api</artifactId>
      <version>${fs-organization-adapter-api.version}</version>
    </dependency>
    <dependency>
      <groupId>org.ehcache</groupId>
      <artifactId>ehcache</artifactId>
      <version>${ehcache.version}</version>
    </dependency>
    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-uc-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.ben-manes.caffeine</groupId>
      <artifactId>caffeine</artifactId>
    </dependency>

    <dependency>
      <groupId>org.aspectj</groupId>
      <artifactId>aspectjweaver</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-aop</artifactId>
    </dependency>

    <dependency>
      <groupId>com.github.colin-lee</groupId>
      <artifactId>jedis-spring-support</artifactId>
    </dependency>

    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>dubbo</artifactId>
    </dependency>

    <dependency>
      <groupId>org.javassist</groupId>
      <artifactId>javassist</artifactId>
    </dependency>

    <dependency>
      <groupId>org.jboss.netty</groupId>
      <artifactId>netty</artifactId>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-bi-metadata-context</artifactId>
      <version>9.4.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-csv</artifactId>
      <version>1.9.0</version>
    </dependency>

    <!--互联平台Service -->
    <dependency>
      <groupId>com.fxiaoke</groupId>
      <artifactId>fs-enterpriserelation-rest-api2</artifactId>
      <version>2.1.1-SNAPSHOT</version>
    </dependency>

    <!--CRM 通知依赖-->
    <dependency>
      <groupId>com.fxiaoke.msg</groupId>
      <artifactId>fs-message-api</artifactId>
      <version>2.0.0-SNAPSHOT</version>
      <exclusions>
        <exclusion>
          <artifactId>fs-restful-server</artifactId>
          <groupId>com.facishare</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.facishare</groupId>
      <artifactId>fs-paas-bizconf-api</artifactId>
      <version>2.0.0-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.fxiaoke.dataplatform</groupId>
      <artifactId>common-utils</artifactId>
      <version>1.0.1-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-junit-jupiter</artifactId>
      <version>5.12.0</version>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-inline</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>net.bytebuddy</groupId>
      <artifactId>byte-buddy</artifactId>
      <version>1.14.14</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.codehaus.gmavenplus</groupId>
        <artifactId>gmavenplus-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.sonarsource.scanner.maven</groupId>
        <artifactId>sonar-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <includes>
            <include>com/unit/*Test.java</include>
            <include>com/unit/*Tests.java</include>
          </includes>
          <argLine>
            --add-opens java.base/java.lang.reflect=ALL-UNNAMED
            --add-opens java.base/java.lang=ALL-UNNAMED
            --add-opens java.base/java.util=ALL-UNNAMED
          </argLine>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>