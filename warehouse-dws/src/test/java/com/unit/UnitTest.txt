package com.unit;

import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncStatusEnum;
import com.fxiaoke.bi.warehouse.common.db.entity.SyncTableStatusEnum;
import com.fxiaoke.bi.warehouse.common.db.entity.DbTableSyncInfoDO;
import com.fxiaoke.bi.warehouse.common.entity.DbSyncInfoFlowDO;
import com.fxiaoke.bi.warehouse.core.db.entity.BIAggSyncInfoDO;
import com.fxiaoke.bi.warehouse.ods.bean.DBSyncInfo;
import com.fxiaoke.bi.warehouse.ods.context.CHContext;
import com.fxiaoke.bi.warehouse.ods.entity.TransferEvent;
import com.fxiaoke.bi.warehouse.ods.entity.DBSyncInfoBO;
import com.fxiaoke.bi.warehouse.ods.compare.util.CompareUtil;
import com.fxiaoke.bi.warehouse.ods.dao.PgCommonDao;

import com.fxiaoke.bi.warehouse.ods.service.DBTransferService;
import com.fxiaoke.bi.warehouse.ods.service.PGMetadataService;
import com.fxiaoke.bi.warehouse.ods.service.CHMetadataService;
import com.fxiaoke.bi.warehouse.ods.service.CHNodeService;
import com.fxiaoke.bi.warehouse.ods.service.CHClientService;
import com.fxiaoke.bi.warehouse.ods.service.CHDBService;
import com.fxiaoke.bi.warehouse.ods.service.MergeTaskService;
import com.fxiaoke.bi.warehouse.ods.service.DbTableSyncInfoService;
import com.fxiaoke.bi.warehouse.ods.integrate.service.impl.IntegrateServiceImpl;
import com.fxiaoke.bi.warehouse.ods.integrate.service.CHDataToCHService;
import com.fxiaoke.bi.warehouse.common.component.ClickHouseUtilService;
import com.fxiaoke.bi.warehouse.common.db.dao.DbSyncInfoFlowDao;
import com.fxiaoke.bi.warehouse.core.db.AggMergeDao;
import com.fxiaoke.bi.warehouse.core.db.PgDataSource;
import com.fxiaoke.bi.warehouse.ods.dao.UdfObjFieldDao;
import com.fxiaoke.bi.warehouse.ods.rout.CHRouterPolicy;
import com.facishare.converter.EIEAConverter;

import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import com.google.common.collect.Sets;
import com.google.common.collect.Maps;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class UnitTest {

    @SpyBean
    private DBTransferService dbTransferService;

    @MockBean
    private PGMetadataService pgMetadataService;

    @MockBean
    private PgCommonDao pgCommonDao;

    @MockBean
    private CHMetadataService chMetadataService;

    @MockBean
    private CHNodeService chNodeService;

    @MockBean
    private CHClientService chClientService;

    @MockBean
    private CHDBService chdbService;

    @MockBean
    private MergeTaskService mergeTaskService;

    @MockBean
    private DbTableSyncInfoService dbTableSyncInfoService;

    @MockBean
    private IntegrateServiceImpl integrateService;

    @MockBean
    private CHDataToCHService chDataToCHService;

    @MockBean
    private ClickHouseUtilService clickHouseUtilService;

    @MockBean
    private DbSyncInfoFlowDao dbSyncInfoFlowDao;

    @MockBean
    private AggMergeDao aggMergeDao;

    @MockBean
    private PgDataSource pgDataSource;

    @MockBean
    private UdfObjFieldDao udfObjFieldDao;

    @MockBean
    private CHRouterPolicy chRouterPolicy;

    @MockBean
    private EIEAConverter eieaConverter;



    @BeforeEach
    public void setUp() {
        // 设置DBTransferService的私有字段，模拟@PostConstruct初始化
        ReflectionTestUtils.setField(dbTransferService, "pgCommonDao", pgCommonDao);
        ReflectionTestUtils.setField(dbTransferService, "pgMetadataService", pgMetadataService);
        ReflectionTestUtils.setField(dbTransferService, "chMetadataService", chMetadataService);
        ReflectionTestUtils.setField(dbTransferService, "chNodeService", chNodeService);
        ReflectionTestUtils.setField(dbTransferService, "chClientService", chClientService);
        ReflectionTestUtils.setField(dbTransferService, "chdbService", chdbService);
        ReflectionTestUtils.setField(dbTransferService, "mergeTaskService", mergeTaskService);
        ReflectionTestUtils.setField(dbTransferService, "dbTableSyncInfoService", dbTableSyncInfoService);
        ReflectionTestUtils.setField(dbTransferService, "integrateService", integrateService);
        ReflectionTestUtils.setField(dbTransferService, "chDataToCHService", chDataToCHService);
        ReflectionTestUtils.setField(dbTransferService, "clickHouseUtilService", clickHouseUtilService);
        ReflectionTestUtils.setField(dbTransferService, "dbSyncInfoFlowDao", dbSyncInfoFlowDao);
        ReflectionTestUtils.setField(dbTransferService, "aggMergeDao", aggMergeDao);
        ReflectionTestUtils.setField(dbTransferService, "pgDataSource", pgDataSource);
        ReflectionTestUtils.setField(dbTransferService, "udfObjFieldDao", udfObjFieldDao);
        ReflectionTestUtils.setField(dbTransferService, "chRouterPolicy", chRouterPolicy);
        ReflectionTestUtils.setField(dbTransferService, "eieaConverter", eieaConverter);

        // 设置一些默认配置值
        ReflectionTestUtils.setField(dbTransferService, "batchSize", 800);
        ReflectionTestUtils.setField(dbTransferService, "savePointSize", 100000);
        ReflectionTestUtils.setField(dbTransferService, "batchQueryBeforeSize", 500);
        ReflectionTestUtils.setField(dbTransferService, "syncDelayThreshold", 1800000L); // 30分钟
        ReflectionTestUtils.setField(dbTransferService, "waitForCalDelayThreshold", 900000L); // 15分钟
        ReflectionTestUtils.setField(dbTransferService, "sendCalHashSalt", 1);
    }

    @Test
    public void testClickhouseDataDao() {
        boolean approximatelyEqual = CompareUtil.isApproximatelyEqual(1000L, 1213L, 10);
        System.out.println(approximatelyEqual);
    }



    @Test
    public void testShouldWait4IncrementMergeMergeAggData() {
        DBSyncInfo dbSyncInfo = new DBSyncInfo();
        dbSyncInfo.setId("6412b13f5cd44942982c91b7");
        dbSyncInfo.setPgDb("*****************************************");
        dbSyncInfo.setChDb("***************************************************");
        dbSyncInfo.setStatus(SyncStatusEnum.SYNC_ABLE.getStatus());
        when(dbTransferService.shouldWait4IncrementMergeMergeAggData(dbSyncInfo)).thenReturn(false);
        boolean result = dbTransferService.shouldWait4IncrementMergeMergeAggData(dbSyncInfo);
        Assert.assertFalse("应该返回false，表示不需要等待", result);
        verify(dbTransferService, times(1)).shouldWait4IncrementMergeMergeAggData(eq(dbSyncInfo));

        log.info("testShouldWait4IncrementMergeMergeAggData 测试通过");
    }

    @Test
    public void testQueryDBSyncInfo() {
        String chDbURL = "***************************************************";
        String dbURL = "*****************************************";
        String schema = "public";

        DBSyncInfo expectedSyncInfo = new DBSyncInfo();
        expectedSyncInfo.setId("6412b13f5cd44942982c91b7");
        expectedSyncInfo.setPgDb(dbURL);
        expectedSyncInfo.setPgSchema(schema);
        expectedSyncInfo.setChDb(chDbURL);
        expectedSyncInfo.setStatus(SyncStatusEnum.SYNC_ABLE.getStatus());

        when(pgCommonDao.queryDBSyncInfo(chDbURL, dbURL, schema)).thenReturn(expectedSyncInfo);

        DBSyncInfo actualSyncInfo = pgCommonDao.queryDBSyncInfo(chDbURL, dbURL, schema);

        Assert.assertNotNull("查询结果不应为空", actualSyncInfo);
        Assert.assertEquals("同步信息ID应该匹配", expectedSyncInfo.getId(), actualSyncInfo.getId());
        Assert.assertEquals("PG数据库URL应该匹配", expectedSyncInfo.getPgDb(), actualSyncInfo.getPgDb());
        Assert.assertEquals("CH数据库URL应该匹配", expectedSyncInfo.getChDb(), actualSyncInfo.getChDb());

        verify(pgCommonDao, times(1)).queryDBSyncInfo(eq(chDbURL), eq(dbURL), eq(schema));

        log.info("testQueryDBSyncInfo 测试通过");
    }

    @Test
    public void testQueryDBSyncInfo_NotFound() {
        String chDbURL = "***************************************************";
        String dbURL = "*****************************************";
        String schema = "public";

        when(pgCommonDao.queryDBSyncInfo(chDbURL, dbURL, schema)).thenReturn(null);

        DBSyncInfo actualSyncInfo = pgCommonDao.queryDBSyncInfo(chDbURL, dbURL, schema);

        Assert.assertNull("查询结果应为空", actualSyncInfo);
        verify(pgCommonDao, times(1)).queryDBSyncInfo(eq(chDbURL), eq(dbURL), eq(schema));

        log.info("testQueryDBSyncInfo_NotFound 测试通过");
    }

    @Test
    public void testUpdateDBSyncInfoStatus() {
        DBSyncInfo dbSyncInfo = new DBSyncInfo();
        dbSyncInfo.setId("6412b13f5cd44942982c91b7");
        dbSyncInfo.setStatus(SyncStatusEnum.SYNC_ING.getStatus());
        dbSyncInfo.setLastModifiedTime(System.currentTimeMillis());

        when(pgCommonDao.updateDBSyncInfoStatus(any(DBSyncInfo.class))).thenReturn(1);

        pgCommonDao.updateDBSyncInfoStatus(dbSyncInfo);

        verify(pgCommonDao, times(1)).updateDBSyncInfoStatus(eq(dbSyncInfo));
        log.info("testUpdateDBSyncInfoStatus 测试通过");
    }

    // ==================== doTranslate 核心方法测试用例 ====================

    /**
     * 测试doTranslate - 正常流程，有有效租户ID
     */
    @Test
    public void testDoTranslate_NormalFlow_WithValidTenants() throws Exception {
        // 准备测试数据
        AtomicLong nextBatchNum = new AtomicLong(1001L);
        TransferEvent transferEvent = createTransferEventForDoTranslate();
        DBSyncInfo dbSyncInfo = createDBSyncInfoForDoTranslate();
        long startTime = System.currentTimeMillis();

        // Mock 租户ID查询 - 返回有效租户
        List<String> validTenantIds = Lists.newArrayList("71570");
        when(pgMetadataService.findValidaTenantId(transferEvent)).thenReturn(validTenantIds);

        // Mock 需要同步的表列表
        Set<String> syncTables = Sets.newHashSet("biz_account", "org_employee_user", "org_dept");
        when(pgMetadataService.findNeededToSyncTables(transferEvent)).thenReturn(syncTables);

        // Mock 表同步信息
        Map<String, DbTableSyncInfoDO> dbTableSyncInfoMap = createMockDbTableSyncInfoMap();
        when(dbTableSyncInfoService.queryDbTableSyncInfoMap(any(DBSyncInfo.class))).thenReturn(dbTableSyncInfoMap);

        // Mock 同步流信息
        DbSyncInfoFlowDO dbSyncInfoFlowDO = createMockDbSyncInfoFlowDO();
        when(dbSyncInfoFlowDao.queryDbSyncFlowAsSyncIng(anyString(), anyString(), anyString(), anyBoolean(), anyInt()))
            .thenReturn(dbSyncInfoFlowDO);

        // Mock 路由检查和添加
        doNothing().when(dbTransferService).checkAndAddChRouter(anyList(), any(DBSyncInfoBO.class));

        // Mock 表级别同步
        doNothing().when(dbTransferService).doTransferByTable(
            any(), any(DBSyncInfoBO.class), anyString(), anyList(), anyMap(), anyString(), any(), anyLong());

        // Mock 用户行为数据同步
        doNothing().when(chDataToCHService).transferOperationDataToCH(
            any(AtomicLong.class), any(DBSyncInfoBO.class), anyList(), anyMap(), anyString());

        // Mock 上下游同步 - executeIntegrateDataPlus返回void
        doNothing().when(integrateService).executeIntegrateDataPlus(any(AtomicLong.class), any(DBSyncInfoBO.class), anyMap(), anyString());

        // Mock 更新同步信息流 - 这是需要Mock的数据库变更操作
        doNothing().when(dbTransferService).updateDbSyncInfoFlow(
            any(DBSyncInfo.class), any(AtomicLong.class), anyMap(), any(DbSyncInfoFlowDO.class));

        // Mock 更新DB同步信息 - 这是需要Mock的数据库变更操作
        doNothing().when(dbTransferService).updateDbSyncInfo(any(DBSyncInfo.class), any(AtomicLong.class), anyList());

        // Mock 发送计算事件 - 这是需要Mock的数据库变更操作
        doNothing().when(pgCommonDao).sendCalculateEvent(any(DBSyncInfo.class), anyInt(), any());

        // 注意：sendBizLog是私有方法，无法Mock，但会在doTranslate中被调用

        // 执行测试
        dbTransferService.doTranslate(nextBatchNum, transferEvent, dbSyncInfo, startTime);

        // 验证关键方法调用
        verify(pgMetadataService, times(1)).findValidaTenantId(transferEvent);
        verify(pgMetadataService, times(1)).findNeededToSyncTables(transferEvent);
        verify(dbTransferService, times(1)).checkAndAddChRouter(eq(validTenantIds), any(DBSyncInfoBO.class));
        verify(chDataToCHService, times(1)).transferOperationDataToCH(
            eq(nextBatchNum), any(DBSyncInfoBO.class), eq(validTenantIds), anyMap(), anyString());
        verify(integrateService, times(1)).executeIntegrateDataPlus(
            eq(nextBatchNum), any(DBSyncInfoBO.class), anyMap(), anyString());
        verify(dbTransferService, times(1)).updateDbSyncInfo(eq(dbSyncInfo), eq(nextBatchNum), eq(validTenantIds));
        verify(pgCommonDao, times(1)).sendCalculateEvent(eq(dbSyncInfo), anyInt(), isNull()); // executeIntegrateDataPlus返回void，所以传null
        // 注意：sendBizLog是私有方法，无法验证，但会在doTranslate中被调用

        log.info("testDoTranslate_NormalFlow_WithValidTenants 测试通过");
    }

    /**
     * 测试doTranslate - 无有效租户ID
     */
    @Test
    public void testDoTranslate_NoValidTenants() throws Exception {
        // 准备测试数据
        AtomicLong nextBatchNum = new AtomicLong(1001L);
        TransferEvent transferEvent = createTransferEventForDoTranslate();
        DBSyncInfo dbSyncInfo = createDBSyncInfoForDoTranslate();
        long startTime = System.currentTimeMillis();

        // Mock 租户ID查询 - 返回空列表
        when(pgMetadataService.findValidaTenantId(transferEvent)).thenReturn(Lists.newArrayList());

        // Mock 更新DB同步信息 - 这是需要Mock的数据库变更操作
        doNothing().when(dbTransferService).updateDbSyncInfo(any(DBSyncInfo.class), any(AtomicLong.class), anyList());

        // Mock 发送计算事件 - 这是需要Mock的数据库变更操作
        doNothing().when(pgCommonDao).sendCalculateEvent(any(DBSyncInfo.class), anyInt(), any());

        // 注意：sendBizLog是私有方法，无法Mock，但会在doTranslate中被调用

        // 执行测试
        dbTransferService.doTranslate(nextBatchNum, transferEvent, dbSyncInfo, startTime);

        // 验证关键方法调用
        verify(pgMetadataService, times(1)).findValidaTenantId(transferEvent);

        // 验证不会执行表同步相关操作
        verify(pgMetadataService, never()).findNeededToSyncTables(any());
        verify(dbTransferService, never()).checkAndAddChRouter(anyList(), any(DBSyncInfoBO.class));
        verify(chDataToCHService, never()).transferOperationDataToCH(any(), any(), anyList(), anyMap(), anyString());
        verify(integrateService, never()).executeIntegrateData(any(), any(), anyMap(), anyString());

        // 验证仍然会执行最终的更新操作
        verify(dbTransferService, times(1)).updateDbSyncInfo(eq(dbSyncInfo), eq(nextBatchNum), eq(Lists.newArrayList()));
        verify(pgCommonDao, times(1)).sendCalculateEvent(eq(dbSyncInfo), anyInt(), isNull());
        // 注意：sendBizLog是私有方法，无法验证，但会在doTranslate中被调用

        log.info("testDoTranslate_NoValidTenants 测试通过");
    }

    /**
     * 测试doTranslate - 支持增量分区的情况
     */
    @Test
    public void testDoTranslate_WithIncrementalPartition() throws Exception {
        // 准备测试数据
        AtomicLong nextBatchNum = new AtomicLong(1001L);
        TransferEvent transferEvent = createTransferEventForDoTranslate();
        DBSyncInfo dbSyncInfo = createDBSyncInfoForDoTranslate();
        dbSyncInfo.setAllowIncPartition(1); // 开启增量分区
        long startTime = System.currentTimeMillis();

        // Mock 租户ID查询
        List<String> validTenantIds = Lists.newArrayList("71570");
        when(pgMetadataService.findValidaTenantId(transferEvent)).thenReturn(validTenantIds);

        // Mock 需要同步的表列表
        Set<String> syncTables = Sets.newHashSet("biz_account");
        when(pgMetadataService.findNeededToSyncTables(transferEvent)).thenReturn(syncTables);

        // Mock 表同步信息
        Map<String, DbTableSyncInfoDO> dbTableSyncInfoMap = createMockDbTableSyncInfoMap();
        when(dbTableSyncInfoService.queryDbTableSyncInfoMap(any(DBSyncInfo.class))).thenReturn(dbTableSyncInfoMap);

        // Mock 同步流信息
        DbSyncInfoFlowDO dbSyncInfoFlowDO = createMockDbSyncInfoFlowDO();
        when(dbSyncInfoFlowDao.queryDbSyncFlowAsSyncIng(anyString(), anyString(), anyString(), anyBoolean(), anyInt()))
            .thenReturn(dbSyncInfoFlowDO);

        // Mock 其他依赖
        doNothing().when(dbTransferService).checkAndAddChRouter(anyList(), any(DBSyncInfoBO.class));
        doNothing().when(dbTransferService).doTransferByTable(any(), any(DBSyncInfoBO.class), anyString(), anyList(), anyMap(), anyString(), any(), anyLong());
        doNothing().when(chDataToCHService).transferOperationDataToCH(any(AtomicLong.class), any(DBSyncInfoBO.class), anyList(), anyMap(), anyString());

        // Mock 增量分区支持的集成服务
        doNothing().when(integrateService).executeIntegrateDataPlus(any(AtomicLong.class), any(DBSyncInfoBO.class), anyMap(), anyString());

        // Mock 更新操作 - 这些是需要Mock的数据库变更操作
        doNothing().when(dbTransferService).updateDbSyncInfoFlow(any(DBSyncInfo.class), any(AtomicLong.class), anyMap(), any(DbSyncInfoFlowDO.class));
        doNothing().when(dbTransferService).updateDbSyncInfo(any(DBSyncInfo.class), any(AtomicLong.class), anyList());
        doNothing().when(pgCommonDao).sendCalculateEvent(any(DBSyncInfo.class), anyInt(), any());
        // 注意：sendBizLog是私有方法，无法Mock，但会在doTranslate中被调用

        // 执行测试
        dbTransferService.doTranslate(nextBatchNum, transferEvent, dbSyncInfo, startTime);

        // 验证调用了增量分区支持的集成方法
        verify(integrateService, times(1)).executeIntegrateDataPlus(eq(nextBatchNum), any(DBSyncInfoBO.class), anyMap(), anyString());
        verify(integrateService, never()).executeIntegrateData(any(), any(), anyMap(), anyString());

        log.info("testDoTranslate_WithIncrementalPartition 测试通过");
    }

    /**
     * 测试doTranslate - 表同步过程中发生异常
     */
    @Test
    public void testDoTranslate_TableSyncException() throws Exception {
        // 准备测试数据
        AtomicLong nextBatchNum = new AtomicLong(1001L);
        TransferEvent transferEvent = createTransferEventForDoTranslate();
        DBSyncInfo dbSyncInfo = createDBSyncInfoForDoTranslate();
        long startTime = System.currentTimeMillis();

        // Mock 租户ID查询
        List<String> validTenantIds = Lists.newArrayList("71570");
        when(pgMetadataService.findValidaTenantId(transferEvent)).thenReturn(validTenantIds);

        // Mock 需要同步的表列表
        Set<String> syncTables = Sets.newHashSet("biz_account");
        when(pgMetadataService.findNeededToSyncTables(transferEvent)).thenReturn(syncTables);

        // Mock 表同步信息
        Map<String, DbTableSyncInfoDO> dbTableSyncInfoMap = createMockDbTableSyncInfoMap();
        when(dbTableSyncInfoService.queryDbTableSyncInfoMap(any(DBSyncInfo.class))).thenReturn(dbTableSyncInfoMap);

        // Mock 同步流信息
        DbSyncInfoFlowDO dbSyncInfoFlowDO = createMockDbSyncInfoFlowDO();
        when(dbSyncInfoFlowDao.queryDbSyncFlowAsSyncIng(anyString(), anyString(), anyString(), anyBoolean(), anyInt()))
            .thenReturn(dbSyncInfoFlowDO);

        // Mock 路由检查
        doNothing().when(dbTransferService).checkAndAddChRouter(anyList(), any(DBSyncInfoBO.class));

        // Mock 表同步抛出异常
        doThrow(new RuntimeException("表同步失败")).when(dbTransferService).doTransferByTable(
            any(), any(DBSyncInfoBO.class), anyString(), anyList(), anyMap(), anyString(), any(), anyLong());

        // Mock 其他操作 - 这些是需要Mock的数据库变更操作
        doNothing().when(dbTransferService).updateDbSyncInfo(any(DBSyncInfo.class), any(AtomicLong.class), anyList());
        doNothing().when(pgCommonDao).sendCalculateEvent(any(DBSyncInfo.class), anyInt(), any());
        // 注意：sendBizLog是私有方法，无法Mock，但会在doTranslate中被调用

        // 执行测试，期望抛出异常
        try {
            dbTransferService.doTranslate(nextBatchNum, transferEvent, dbSyncInfo, startTime);
            Assert.fail("应该抛出RuntimeException");
        } catch (RuntimeException e) {
            Assert.assertTrue("异常消息应该包含表同步失败", e.getMessage().contains("表同步失败"));
        }

        // 验证基本方法仍然被调用
        verify(pgMetadataService, times(1)).findValidaTenantId(transferEvent);
        verify(pgMetadataService, times(1)).findNeededToSyncTables(transferEvent);

        log.info("testDoTranslate_TableSyncException 测试通过");
    }

    /**
     * 测试doTranslate - 大量表的分区处理
     */
    @Test
    public void testDoTranslate_LargeTablePartition() throws Exception {
        // 准备测试数据
        AtomicLong nextBatchNum = new AtomicLong(1001L);
        TransferEvent transferEvent = createTransferEventForDoTranslate();
        DBSyncInfo dbSyncInfo = createDBSyncInfoForDoTranslate();
        long startTime = System.currentTimeMillis();

        // Mock 租户ID查询
        List<String> validTenantIds = Lists.newArrayList("71570");
        when(pgMetadataService.findValidaTenantId(transferEvent)).thenReturn(validTenantIds);

        // Mock 大量需要同步的表列表（超过分区大小）
        Set<String> syncTables = Sets.newHashSet();
        for (int i = 1; i <= 25; i++) { // 假设分区大小为10，这里创建25个表
            syncTables.add("table_" + i);
        }
        when(pgMetadataService.findNeededToSyncTables(transferEvent)).thenReturn(syncTables);

        // Mock 表同步信息
        Map<String, DbTableSyncInfoDO> dbTableSyncInfoMap = createMockDbTableSyncInfoMap();
        when(dbTableSyncInfoService.queryDbTableSyncInfoMap(any(DBSyncInfo.class))).thenReturn(dbTableSyncInfoMap);

        // Mock 同步流信息
        DbSyncInfoFlowDO dbSyncInfoFlowDO = createMockDbSyncInfoFlowDO();
        when(dbSyncInfoFlowDao.queryDbSyncFlowAsSyncIng(anyString(), anyString(), anyString(), anyBoolean(), anyInt()))
            .thenReturn(dbSyncInfoFlowDO);

        // Mock 其他依赖
        doNothing().when(dbTransferService).checkAndAddChRouter(anyList(), any(DBSyncInfoBO.class));
        doNothing().when(dbTransferService).doTransferByTable(any(), any(DBSyncInfoBO.class), anyString(), anyList(), anyMap(), anyString(), any(), anyLong());
        doNothing().when(chDataToCHService).transferOperationDataToCH(any(AtomicLong.class), any(DBSyncInfoBO.class), anyList(), anyMap(), anyString());

        BIAggSyncInfoDO mockBIAggSyncInfoDO = createMockBIAggSyncInfoDO();
        when(integrateService.executeIntegrateData(any(AtomicLong.class), any(DBSyncInfoBO.class), anyMap(), anyString()))
            .thenReturn(mockBIAggSyncInfoDO);

        // Mock 更新操作 - 这些是需要Mock的数据库变更操作
        doNothing().when(dbTransferService).updateDbSyncInfoFlow(any(DBSyncInfo.class), any(AtomicLong.class), anyMap(), any(DbSyncInfoFlowDO.class));
        doNothing().when(dbTransferService).updateDbSyncInfo(any(DBSyncInfo.class), any(AtomicLong.class), anyList());
        doNothing().when(pgCommonDao).sendCalculateEvent(any(DBSyncInfo.class), anyInt(), any());
        // 注意：sendBizLog是私有方法，无法Mock，但会在doTranslate中被调用

        // 执行测试
        dbTransferService.doTranslate(nextBatchNum, transferEvent, dbSyncInfo, startTime);

        // 验证表同步被调用了25次（每个表一次）
        verify(dbTransferService, times(25)).doTransferByTable(
            any(), any(DBSyncInfoBO.class), anyString(), eq(validTenantIds), anyMap(), anyString(), any(), anyLong());

        // 验证其他关键方法调用
        verify(pgMetadataService, times(1)).findValidaTenantId(transferEvent);
        verify(pgMetadataService, times(1)).findNeededToSyncTables(transferEvent);
        verify(chDataToCHService, times(1)).transferOperationDataToCH(eq(nextBatchNum), any(DBSyncInfoBO.class), eq(validTenantIds), anyMap(), anyString());

        log.info("testDoTranslate_LargeTablePartition 测试通过");
    }

    /**
     * 测试doTranslate - 集成服务异常处理
     */
    @Test
    public void testDoTranslate_IntegrateServiceException() throws Exception {
        // 准备测试数据
        AtomicLong nextBatchNum = new AtomicLong(1001L);
        TransferEvent transferEvent = createTransferEventForDoTranslate();
        DBSyncInfo dbSyncInfo = createDBSyncInfoForDoTranslate();
        long startTime = System.currentTimeMillis();

        // Mock 租户ID查询
        List<String> validTenantIds = Lists.newArrayList("71570");
        when(pgMetadataService.findValidaTenantId(transferEvent)).thenReturn(validTenantIds);

        // Mock 需要同步的表列表
        Set<String> syncTables = Sets.newHashSet("biz_account");
        when(pgMetadataService.findNeededToSyncTables(transferEvent)).thenReturn(syncTables);

        // Mock 表同步信息
        Map<String, DbTableSyncInfoDO> dbTableSyncInfoMap = createMockDbTableSyncInfoMap();
        when(dbTableSyncInfoService.queryDbTableSyncInfoMap(any(DBSyncInfo.class))).thenReturn(dbTableSyncInfoMap);

        // Mock 同步流信息
        DbSyncInfoFlowDO dbSyncInfoFlowDO = createMockDbSyncInfoFlowDO();
        when(dbSyncInfoFlowDao.queryDbSyncFlowAsSyncIng(anyString(), anyString(), anyString(), anyBoolean(), anyInt()))
            .thenReturn(dbSyncInfoFlowDO);

        // Mock 其他依赖
        doNothing().when(dbTransferService).checkAndAddChRouter(anyList(), any(DBSyncInfoBO.class));
        doNothing().when(dbTransferService).doTransferByTable(any(), any(DBSyncInfoBO.class), anyString(), anyList(), anyMap(), anyString(), any(), anyLong());
        doNothing().when(chDataToCHService).transferOperationDataToCH(any(AtomicLong.class), any(DBSyncInfoBO.class), anyList(), anyMap(), anyString());

        // Mock 集成服务抛出异常 - 需要同时Mock两个方法，因为代码会根据灰度规则选择调用哪个
        when(integrateService.executeIntegrateData(any(AtomicLong.class), any(DBSyncInfoBO.class), anyMap(), anyString()))
            .thenThrow(new RuntimeException("集成服务异常"));
        doThrow(new RuntimeException("集成服务异常"))
            .when(integrateService).executeIntegrateDataPlus(any(AtomicLong.class), any(DBSyncInfoBO.class), anyMap(), anyString());

        // Mock 更新操作 - 这些是需要Mock的数据库变更操作
        doNothing().when(dbTransferService).updateDbSyncInfo(any(DBSyncInfo.class), any(AtomicLong.class), anyList());
        doNothing().when(pgCommonDao).sendCalculateEvent(any(DBSyncInfo.class), anyInt(), any());
        // 注意：sendBizLog是私有方法，无法Mock，但会在doTranslate中被调用

        // 执行测试，期望抛出异常
        try {
            dbTransferService.doTranslate(nextBatchNum, transferEvent, dbSyncInfo, startTime);
            Assert.fail("应该抛出RuntimeException");
        } catch (RuntimeException e) {
            Assert.assertTrue("异常消息应该包含集成服务异常", e.getMessage().contains("集成服务异常"));
        }

        // 验证基本方法仍然被调用
        verify(pgMetadataService, times(1)).findValidaTenantId(transferEvent);
        verify(chDataToCHService, times(1)).transferOperationDataToCH(any(), any(), anyList(), anyMap(), anyString());

        // 验证集成服务方法被调用（可能是executeIntegrateData或executeIntegrateDataPlus中的一个）
        // 由于灰度规则的存在，我们需要验证至少有一个被调用
        try {
            verify(integrateService, times(1)).executeIntegrateData(any(), any(), anyMap(), anyString());
        } catch (AssertionError e1) {
            // 如果executeIntegrateData没有被调用，验证executeIntegrateDataPlus被调用
            verify(integrateService, times(1)).executeIntegrateDataPlus(any(), any(), anyMap(), anyString());
        }

        log.info("testDoTranslate_IntegrateServiceException 测试通过");
    }





    // ==================== doTranslate 测试辅助方法 ====================

    /**
     * 创建测试用的TransferEvent for doTranslate
     */
    private TransferEvent createTransferEventForDoTranslate() {
        TransferEvent transferEvent = new TransferEvent();
        transferEvent.setChDbURL("***************************************************");
        transferEvent.setDbURL("*****************************************");
        transferEvent.setSchema("public");
        return transferEvent;
    }

    /**
     * 创建测试用的DBSyncInfo for doTranslate
     */
    private DBSyncInfo createDBSyncInfoForDoTranslate() {
        DBSyncInfo dbSyncInfo = new DBSyncInfo();
        dbSyncInfo.setId("6412b13f5cd44942982c91b7");
        dbSyncInfo.setPgDb("*****************************************");
        dbSyncInfo.setPgSchema("public");
        dbSyncInfo.setChDb("***************************************************");
        dbSyncInfo.setStatus(SyncStatusEnum.SYNC_ING.getStatus());
        dbSyncInfo.setBatchNum(1000L);
        dbSyncInfo.setLastModifiedTime(System.currentTimeMillis());
        dbSyncInfo.setLastSyncTime(System.currentTimeMillis());
        dbSyncInfo.setCreateTime(System.currentTimeMillis());
        dbSyncInfo.setIsDeleted(0);
        dbSyncInfo.setAllowIncPartition(0);
        dbSyncInfo.setAllowPaas2biStatus(0);
        dbSyncInfo.setLastMergeAggTime(System.currentTimeMillis());
        dbSyncInfo.setLastSyncEis("71570");
        dbSyncInfo.setLastIntegrateTime(System.currentTimeMillis());
        return dbSyncInfo;
    }

    /**
     * 创建测试用的DbTableSyncInfoMap
     */
    private Map<String, DbTableSyncInfoDO> createMockDbTableSyncInfoMap() {
        Map<String, DbTableSyncInfoDO> map = Maps.newHashMap();

        DbTableSyncInfoDO dbTableSyncInfoDO1 = new DbTableSyncInfoDO();
        dbTableSyncInfoDO1.setId("66bc603ab4e39a46946c840c");
        dbTableSyncInfoDO1.setTableName("biz_account");
        dbTableSyncInfoDO1.setDbSyncId("6412b13f5cd44942982c91b7");
        dbTableSyncInfoDO1.setMaxSysModifiedTime(System.currentTimeMillis());
        dbTableSyncInfoDO1.setLastSyncTime(System.currentTimeMillis());
        dbTableSyncInfoDO1.setBatchNum(1000L);
        dbTableSyncInfoDO1.setStatus(SyncTableStatusEnum.SYNCED.getStatus());
        dbTableSyncInfoDO1.setCreateTime(System.currentTimeMillis());
        dbTableSyncInfoDO1.setLastModifiedTime(System.currentTimeMillis());
        dbTableSyncInfoDO1.setIsDeleted(0);
        map.put("biz_account", dbTableSyncInfoDO1);

        DbTableSyncInfoDO dbTableSyncInfoDO2 = new DbTableSyncInfoDO();
        dbTableSyncInfoDO2.setId("66bc6155b4e39a46946ca030");
        dbTableSyncInfoDO2.setTableName("org_employee_user");
        dbTableSyncInfoDO2.setDbSyncId("6412b13f5cd44942982c91b7");
        dbTableSyncInfoDO2.setMaxSysModifiedTime(System.currentTimeMillis());
        dbTableSyncInfoDO2.setLastSyncTime(System.currentTimeMillis());
        dbTableSyncInfoDO2.setBatchNum(1000L);
        dbTableSyncInfoDO2.setStatus(SyncTableStatusEnum.SYNCED.getStatus());
        dbTableSyncInfoDO2.setCreateTime(System.currentTimeMillis());
        dbTableSyncInfoDO2.setLastModifiedTime(System.currentTimeMillis());
        dbTableSyncInfoDO2.setIsDeleted(0);
        map.put("org_employee_user", dbTableSyncInfoDO2);

        return map;
    }

    /**
     * 创建测试用的DbSyncInfoFlowDO
     */
    private DbSyncInfoFlowDO createMockDbSyncInfoFlowDO() {
        DbSyncInfoFlowDO flowDO = new DbSyncInfoFlowDO();
        flowDO.setId("flow_id_123");
        flowDO.setDbSyncId("6412b13f5cd44942982c91b7");
        flowDO.setBatchNums(new Long[]{1000L, 1001L}); // 使用batchNums数组而不是batchNum
        flowDO.setStatus(SyncStatusEnum.SYNC_ING.getStatus());
        flowDO.setCreateTime(System.currentTimeMillis());
        flowDO.setLastSyncTime(System.currentTimeMillis());
        flowDO.setApiNameEiMap("{}");
        flowDO.setTenantId("-1");
        flowDO.setPartitionName("inc_partition");
        flowDO.setLastModifiedTime(System.currentTimeMillis());
        flowDO.setVersion(0);
        flowDO.setIsDeleted(0);
        return flowDO;
    }

    /**
     * 创建测试用的BIAggSyncInfoDO
     */
    private BIAggSyncInfoDO createMockBIAggSyncInfoDO() {
        BIAggSyncInfoDO aggSyncInfo = new BIAggSyncInfoDO();
        aggSyncInfo.setId("agg_sync_123");
        aggSyncInfo.setTenantId("12345");
        aggSyncInfo.setStatus(SyncStatusEnum.SYNC_ED.getStatus());
        aggSyncInfo.setBatchNum(1001L);
        aggSyncInfo.setLastSyncTime(System.currentTimeMillis());
        aggSyncInfo.setCreateTime(System.currentTimeMillis());
        aggSyncInfo.setLastModifiedTime(System.currentTimeMillis());
        aggSyncInfo.setIsDeleted(0);
        aggSyncInfo.setVersion(1);
        return aggSyncInfo;
    }

    // ==================== 其他测试辅助方法 ====================



    /**
     * 创建测试用的DBSyncInfo
     */
    private DBSyncInfo createDBSyncInfo(SyncStatusEnum status) {
        DBSyncInfo dbSyncInfo = new DBSyncInfo();
        dbSyncInfo.setId("6412b13f5cd44942982c91b7");
        dbSyncInfo.setPgDb("*****************************************");
        dbSyncInfo.setPgSchema("public");
        dbSyncInfo.setChDb("***************************************************");
        dbSyncInfo.setStatus(status.getStatus());
        dbSyncInfo.setBatchNum(1000L);
        dbSyncInfo.setLastModifiedTime(System.currentTimeMillis());
        dbSyncInfo.setLastSyncTime(System.currentTimeMillis());
        dbSyncInfo.setCreateTime(System.currentTimeMillis());
        dbSyncInfo.setIsDeleted(0);
        dbSyncInfo.setAllowIncPartition(0);
        dbSyncInfo.setAllowPaas2biStatus(0);
        return dbSyncInfo;
    }

    // ==================== 边界条件和特殊场景测试 ====================

    /**
     * 测试shouldWait4IncrementMergeMergeAggData - 正常情况返回true
     */
    @Test
    public void testShouldWait4IncrementMergeMergeAggData_ReturnTrue() {
        try (MockedStatic<CHContext> mockedStatic = mockStatic(CHContext.class)) {
            DBSyncInfo dbSyncInfo = createDBSyncInfo(SyncStatusEnum.AGG_ED);
            mockedStatic.when(() -> CHContext.shouldMerge(dbSyncInfo.getChDb())).thenReturn(true);

            // Mock CAS更新返回1，表示更新成功
            when(pgCommonDao.updateDbSyncInfoByIdStatusCAS(anyInt(), anyList(), anyList())).thenReturn(1);

            boolean result = dbTransferService.shouldWait4IncrementMergeMergeAggData(dbSyncInfo);

            Assert.assertTrue("应该返回true，表示需要等待merge", result);
            verify(pgCommonDao, times(1)).updateDbSyncInfoByIdStatusCAS(
                    eq(SyncStatusEnum.EXCHANGE_AGG.getStatus()),
                    eq(Lists.newArrayList(dbSyncInfo.getId())),
                    anyList()
            );

            log.info("testShouldWait4IncrementMergeMergeAggData_ReturnTrue 测试通过");
        }
    }

    /**
     * 测试shouldWait4IncrementMergeMergeAggData - CAS更新失败返回true
     */
    @Test
    public void testShouldWait4IncrementMergeMergeAggData_CASFailed() {
        try (MockedStatic<CHContext> mockedStatic = mockStatic(CHContext.class)) {
            DBSyncInfo dbSyncInfo = createDBSyncInfo(SyncStatusEnum.AGG_ED);
            mockedStatic.when(() -> CHContext.shouldMerge(dbSyncInfo.getChDb())).thenReturn(true);

            // Mock CAS更新返回0，表示更新失败
            when(pgCommonDao.updateDbSyncInfoByIdStatusCAS(anyInt(), anyList(), anyList())).thenReturn(0);

            boolean result = dbTransferService.shouldWait4IncrementMergeMergeAggData(dbSyncInfo);

            Assert.assertTrue("即使CAS失败也应该返回true", result);
            verify(pgCommonDao, times(1)).updateDbSyncInfoByIdStatusCAS(anyInt(), anyList(), anyList());

            log.info("testShouldWait4IncrementMergeMergeAggData_CASFailed 测试通过");
        }
    }

    /**
     * 测试createCHNodeInfo - 正常情况
     */
    @Test
    public void testCreateCHNodeInfo_Success() {
        String dbSyncId = "6412b13f5cd44942982c91b7";
        DBSyncInfo dbSyncInfo = createDBSyncInfo(SyncStatusEnum.SYNC_ABLE);

        when(pgCommonDao.queryDBSyncInfoById(anyList())).thenReturn(Lists.newArrayList(dbSyncInfo));

        var result = dbTransferService.createCHNodeInfo(dbSyncId);

        Assert.assertNotNull("应该返回ClickhouseNodeInfo对象", result);
        verify(pgCommonDao, times(1)).queryDBSyncInfoById(eq(Lists.newArrayList(dbSyncId)));

        log.info("testCreateCHNodeInfo_Success 测试通过");
    }

    /**
     * 测试createCHNodeInfo - 找不到同步信息
     */
    @Test
    public void testCreateCHNodeInfo_NotFound() {
        String dbSyncId = "nonexistent";

        when(pgCommonDao.queryDBSyncInfoById(anyList())).thenReturn(Lists.newArrayList());

        var result = dbTransferService.createCHNodeInfo(dbSyncId);

        Assert.assertNull("应该返回null", result);
        verify(pgCommonDao, times(1)).queryDBSyncInfoById(eq(Lists.newArrayList(dbSyncId)));

        log.info("testCreateCHNodeInfo_NotFound 测试通过");
    }



    /**
     * 测试updateDbSyncInfo和updateDbSyncInfoFlow方法
     */
    @Test
    public void testUpdateDbSyncInfo() {
        DBSyncInfo dbSyncInfo = createDBSyncInfo(SyncStatusEnum.SYNC_ED);
        AtomicLong nextBatchNum = new AtomicLong(1001L);
        List<String> tenantIdList = Lists.newArrayList("123", "456");

        // Mock 数据库变更操作
        when(pgCommonDao.updateDbSyncInfo(any(DBSyncInfo.class))).thenReturn(1);

        // 执行测试
        dbTransferService.updateDbSyncInfo(dbSyncInfo, nextBatchNum, tenantIdList);

        // 验证调用
        verify(pgCommonDao, times(1)).updateDbSyncInfo(any(DBSyncInfo.class));

        log.info("testUpdateDbSyncInfo 测试通过");
    }



    /**
     * 测试性能相关的配置参数
     */
    @Test
    public void testPerformanceConfiguration() {
        // 验证配置参数是否正确设置
        Integer batchSize = (Integer) ReflectionTestUtils.getField(dbTransferService, "batchSize");
        Integer savePointSize = (Integer) ReflectionTestUtils.getField(dbTransferService, "savePointSize");
        Integer batchQueryBeforeSize = (Integer) ReflectionTestUtils.getField(dbTransferService, "batchQueryBeforeSize");

        Assert.assertEquals("batchSize应该为800", Integer.valueOf(800), batchSize);
        Assert.assertEquals("savePointSize应该为100000", Integer.valueOf(100000), savePointSize);
        Assert.assertEquals("batchQueryBeforeSize应该为500", Integer.valueOf(500), batchQueryBeforeSize);

        log.info("testPerformanceConfiguration 测试通过");
    }
}
