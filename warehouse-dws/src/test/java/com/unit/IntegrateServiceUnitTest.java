package com.unit;

import com.fxiaoke.bi.warehouse.ServerApplication;
import com.fxiaoke.bi.warehouse.common.db.entity.DbTableSyncInfoDO;
import com.fxiaoke.bi.warehouse.common.util.GrayManager;
import com.fxiaoke.bi.warehouse.common.util.WarehouseConfig;
import com.fxiaoke.bi.warehouse.ods.entity.DBSyncInfoBO;
import com.fxiaoke.bi.warehouse.ods.integrate.model.TopologyTableIntegrateBO;
import com.fxiaoke.bi.warehouse.ods.integrate.service.*;
import com.fxiaoke.bi.warehouse.ods.integrate.service.impl.IntegrateServiceImpl;
import com.fxiaoke.bi.warehouse.common.bean.TopologyTableAggDownStream;
import com.fxiaoke.bi.warehouse.ods.service.CHDataSource;
import com.fxiaoke.bi.warehouse.ods.service.CHMetadataService;
import com.fxiaoke.bi.warehouse.ods.entity.ClickhouseTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ServerApplication.class)
public class IntegrateServiceUnitTest {

    @SpyBean
    private DataSyncLicenseService dataSyncLicenseService;

    @SpyBean
    private BiMtTopologyTableService biMtTopologyTableService;

    @SpyBean
    private BiDataSyncPolicyService biDataSyncPolicyService;

    @SpyBean
    private AggDownStreamService aggDownStreamService;

    @SpyBean
    private AggDataSyncInfoService aggDataSyncInfoService;

    @SpyBean
    private CHDataSource chDataSource;

    @SpyBean
    private RptMessageService rptMessageService;

    @MockBean
    private CHMetadataService chMetadataService;

    @SpyBean
    private IntegrateServiceImpl integrateService;

    private MockedStatic<GrayManager> grayManagerMock;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);

        // 初始化静态Mock
        grayManagerMock = mockStatic(GrayManager.class);

        // 设置WarehouseConfig的默认值
        ReflectionTestUtils.setField(WarehouseConfig.class, "integrateBeginTime", 0L);
        ReflectionTestUtils.setField(WarehouseConfig.class, "integrateEndTime", 1440L); // 全天
        ReflectionTestUtils.setField(WarehouseConfig.class, "allowIntegrate", true);

        // Mock CHMetadataService的方法，避免真实数据库调用
        when(chMetadataService.loadTableFromDB(anyString(), anyString())).thenReturn(Optional.of(createMockClickhouseTable()));
    }

    @AfterEach
    public void tearDown() {
        if (grayManagerMock != null) {
            grayManagerMock.close();
        }
    }

    /**
     * 测试executeIntegrateDataPlus - 正常执行流程
     */
    @Test
    public void testExecuteIntegrateDataPlus_NormalFlow() {
        AtomicLong nextBatchNum = new AtomicLong(1001L);
        DBSyncInfoBO dbSyncInfoCopy = createDBSyncInfoBO();
        Map<String, DbTableSyncInfoDO> dbTableSyncInfoMap = createDbTableSyncInfoMap();
        String partitionName = WarehouseConfig.STOCK_PARTITION_NAME;
        String tenantId = "82958";
        grayManagerMock.when(() -> GrayManager.isAllowByRule("agg_downstream_data_eis", tenantId)).thenReturn(true);
        grayManagerMock.when(() -> GrayManager.isAllowByRule("biDataSyncPolicyGray", tenantId)).thenReturn(false);
        grayManagerMock.when(() -> GrayManager.isAllowByRule("query_from_tenant_db", String.format("%s^%s", anyString(), anyString()))).thenReturn(true);
        doNothing().when(aggDataSyncInfoService).updateAggDataSyncInfoBatch(anyString(), anyList());
        doNothing().when(aggDataSyncInfoService).insertBiDataSyncPolicyLog(anyString(), anyString(), anyString(), anyString(), anyString());
        doNothing().when(rptMessageService).pushRptMessage(anyInt(), anyList(), anyString(), anyString(), anyString());
        integrateService.executeIntegrateDataPlus(nextBatchNum, dbSyncInfoCopy, dbTableSyncInfoMap, partitionName);
        verify(aggDownStreamService, never()).synchronizeDownstreamAggDataNew(anyString(), anyLong(), any(), anyString());
        log.info("testExecuteIntegrateDataPlus_NormalFlow 测试通过");
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建测试用的DBSyncInfoBO
     */
    private DBSyncInfoBO createDBSyncInfoBO() {
        DBSyncInfoBO dbSyncInfoBO = new DBSyncInfoBO();
        dbSyncInfoBO.setId("64ee0f5dbfbe303944dd65b6");
        dbSyncInfoBO.setPgDb("***********************************************");
        dbSyncInfoBO.setPgSchema("sch_82958");
        dbSyncInfoBO.setChDb("***************************************************");
        dbSyncInfoBO.setLastIntegrateTime(1750753932761L);
        dbSyncInfoBO.setBatchNum(74622L);
        dbSyncInfoBO.setCreateTime(1693323101956L);
        dbSyncInfoBO.setLastModifiedTime(1750753937181L);
        dbSyncInfoBO.setLastSyncTime(1750753877296L);
        dbSyncInfoBO.setLastMergeAggTime(1736007127374L);
        return dbSyncInfoBO;
    }

    /**
     * 创建测试用的DbTableSyncInfoMap
     */
    private Map<String, DbTableSyncInfoDO> createDbTableSyncInfoMap() {
        Map<String, DbTableSyncInfoDO> map = Maps.newHashMap();

        DbTableSyncInfoDO syncInfo1 = new DbTableSyncInfoDO();
        syncInfo1.setId("684365bd6deb8710f2c1f1d7");
        syncInfo1.setTableName("biz_account_downstream");
        syncInfo1.setDbSyncId("672dfbb017bb731c530477cd");
        syncInfo1.setBatchNum(13162L);
        syncInfo1.setLastSyncTime(1750716226937L);
        syncInfo1.setLastModifiedTime(1750716227180L);
        syncInfo1.setCreateTime(1749247421698L);
        map.put("biz_account_downstream", syncInfo1);

        DbTableSyncInfoDO syncInfo2 = new DbTableSyncInfoDO();
        syncInfo2.setId("684365be6deb8710f2c1f1d8");
        syncInfo2.setTableName("agg_downstream_data");
        syncInfo2.setDbSyncId("672dfbb017bb731c530477cd");
        syncInfo2.setBatchNum(13162L);
        syncInfo2.setLastSyncTime(1750716226937L);
        syncInfo2.setLastModifiedTime(1750716227180L);
        syncInfo2.setCreateTime(1749247421698L);
        map.put("agg_downstream_data", syncInfo2);
        return map;
    }

    /**
     * 创建测试用的拓扑表列表
     */
    private List<TopologyTableIntegrateBO> createTopologyTableList() {
        List<TopologyTableIntegrateBO> list = Lists.newArrayList();

        // 创建Mock的TopologyTableAggDownStream
        TopologyTableAggDownStream aggDownStream1 = mock(TopologyTableAggDownStream.class);
        when(aggDownStream1.getUpTables()).thenReturn(Sets.newHashSet("biz_account"));

        TopologyTableAggDownStream aggDownStream2 = mock(TopologyTableAggDownStream.class);
        when(aggDownStream2.getUpTables()).thenReturn(Sets.newHashSet("org_employee"));

        TopologyTableIntegrateBO table1 = TopologyTableIntegrateBO.builder()
                .tenantId("12345")
                .sourceId("source1")
                .source(0)
                .statFieldLocation(Maps.newHashMap())
                .uniqFieldLocation(Maps.newHashMap())
                .aggDownstreamJson(aggDownStream1)
                .status(1)
                .build();
        list.add(table1);

        TopologyTableIntegrateBO table2 = TopologyTableIntegrateBO.builder()
                .tenantId("12345")
                .sourceId("source2")
                .source(0)
                .statFieldLocation(Maps.newHashMap())
                .uniqFieldLocation(Maps.newHashMap())
                .aggDownstreamJson(aggDownStream2)
                .status(1)
                .build();
        list.add(table2);

        return list;
    }

    /**
     * 创建Mock的ClickhouseTable
     */
    private ClickhouseTable createMockClickhouseTable() {
        ClickhouseTable table = new ClickhouseTable();
        table.setName("biz_account_downstream");
        table.setDb("test_db");
        table.setDbURL("****************************************");
        table.setColumnList(Lists.newArrayList());
        table.setColumnMap(Maps.newHashMap());
        table.setOrderByColumns(Lists.newArrayList());
        return table;
    }
}
